# Psychometric Testing Platform - AI Module

A comprehensive AI-powered psychometric testing platform for students (Grades 1-12) featuring question generation, automated scoring, report generation, and external data integration.

## Features

### 1. Question Bank Generation
- Pre-generates 60 multiple-choice questions per grade (1-12)
- Covers RIASEC, Big 5, Cognitive, Hogan, and CPI theories
- Uses Gemini 2.5 Flash for intelligent question generation
- Consistent JSON schema with validation

### 2. Scoring Engine
- **RIASEC Scoring**: Career interest assessment with Person-Environment fit analysis
- **Big Five Scoring**: Personality trait evaluation (0.0-5.0 scale)
- **Cognitive Scoring**: Reasoning and problem-solving assessment (0-100%)
- **Career Matching**: Cosine similarity with O*NET RIASEC vectors

### 3. Report Generation
- Jinja2 templates for PDF/HTML reports
- AI-polished narrative summaries using Gemini
- WeasyPrint for PDF generation with fallback to HTML
- Comprehensive student profiles with visualizations

### 4. External Data Integration
- Scholarship and loan data fetching from APIs
- Data normalization and validation
- Sentence transformer embeddings for semantic search
- Vector storage preparation (pseudo-code included)

### 5. FastAPI Integration
- RESTful API with comprehensive endpoints
- Session management for test administration
- Background task processing
- CORS support and error handling

## Project Structure

```
psychometric/
├── requirements.txt          # Python dependencies
├── .env.example             # Environment variables template
├── main.py                  # FastAPI application
├── config/
│   └── settings.py          # Configuration management
├── ai_module/
│   ├── __init__.py
│   ├── question_gen.py      # Question generation with Gemini
│   ├── scoring.py           # Psychometric scoring algorithms
│   ├── report_gen.py        # PDF report generation
│   └── data_ingestion.py    # External data fetching
├── templates/
│   └── report_template.html # Jinja2 report template
├── data/
│   ├── onet_riasec.json     # O*NET career cluster data
│   └── question_schemas.json # JSON schemas and mappings
├── static/
│   └── reports/             # Generated PDF reports
└── tests/
    ├── test_question_gen.py  # Question generation tests
    ├── test_scoring.py       # Scoring algorithm tests
    └── test_report_gen.py    # Report generation tests
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd psychometric
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

5. **Configure Gemini API**
   - Get your API key from Google AI Studio
   - Add it to your `.env` file: `GEMINI_API_KEY=your_key_here`

## Usage

### Starting the Server

```bash
python main.py
```

The API will be available at `http://localhost:8000`

### API Documentation

Visit `http://localhost:8000/docs` for interactive API documentation.

### Key Endpoints

- `POST /generate_questions` - Generate psychometric questions
- `POST /create_session` - Create a new test session
- `POST /submit_test` - Submit student responses
- `GET /generate_report?session_id=...` - Generate PDF report
- `GET /scholarships?grade=...` - Fetch scholarships
- `GET /loans?grade=...` - Fetch loans/financial aid

### Example Usage

1. **Create a test session**
   ```python
   import requests
   
   response = requests.post("http://localhost:8000/create_session", json={
       "student_name": "John Doe",
       "grade": 10,
       "theories": ["RIASEC", "Big5", "Cognitive"]
   })
   session_id = response.json()["session_id"]
   ```

2. **Generate questions**
   ```python
   response = requests.post("http://localhost:8000/generate_questions", json={
       "grade": 10,
       "theory": "RIASEC",
       "trait": "Realistic",
       "difficulty": "medium",
       "num_questions": 5
   })
   questions = response.json()["questions"]
   ```

3. **Submit test responses**
   ```python
   responses = [
       {
           "question_id": "q1",
           "theory": "RIASEC",
           "trait": "Realistic",
           "selected_option": 2
       }
       # ... more responses
   ]
   
   requests.post("http://localhost:8000/submit_test", json={
       "session_id": session_id,
       "student_name": "John Doe",
       "grade": 10,
       "responses": responses
   })
   ```

4. **Generate report**
   ```python
   response = requests.get(f"http://localhost:8000/generate_report?session_id={session_id}")
   # Downloads PDF report
   ```

## Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
pytest

# Run specific test file
pytest tests/test_question_gen.py

# Run with coverage
pytest --cov=ai_module tests/
```

## Configuration

### Environment Variables

- `GEMINI_API_KEY`: Google Gemini API key (required)
- `DATABASE_URL`: Database connection string
- `SCHOLARSHIPS_API_URL`: External scholarships API endpoint
- `LOANS_API_URL`: External loans API endpoint
- `REPORTS_DIR`: Directory for generated reports
- `DEBUG`: Enable debug mode (True/False)

### Psychometric Theories

The platform supports five psychometric theories:

1. **RIASEC**: Career interest assessment (Holland Codes)
2. **Big Five**: Personality traits (OCEAN model)
3. **Cognitive**: Reasoning and problem-solving abilities
4. **Hogan**: Workplace personality assessment
5. **CPI**: California Psychological Inventory

## Development

### Adding New Theories

1. Update `config/settings.py` with new theory traits
2. Add scoring logic in `ai_module/scoring.py`
3. Update question generation prompts in `ai_module/question_gen.py`
4. Add tests for the new theory

### Customizing Reports

1. Modify `templates/report_template.html` for layout changes
2. Update `ai_module/report_gen.py` for data processing
3. Adjust CSS styles in the template for visual customization

### External Data Integration

1. Implement API clients in `ai_module/data_ingestion.py`
2. Add data normalization functions
3. Configure API endpoints in settings

## Production Deployment

### Security Considerations

- Use environment variables for sensitive data
- Implement proper authentication and authorization
- Enable HTTPS in production
- Validate and sanitize all inputs
- Use a proper database instead of in-memory storage

### Performance Optimization

- Implement caching for frequently accessed data
- Use background tasks for heavy operations
- Consider using a message queue for question generation
- Optimize database queries and indexing

### Monitoring

- Add logging and monitoring
- Implement health checks
- Set up error tracking
- Monitor API performance and usage

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]

## Support

[Add support contact information here]
