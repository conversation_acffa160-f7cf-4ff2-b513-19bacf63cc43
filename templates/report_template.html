<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Psychometric Assessment Report - {{ student_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-top: 10px;
        }
        
        .student-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .student-info h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        .info-item {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .info-label {
            font-weight: bold;
            color: #555;
        }
        
        .section {
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .summary-text {
            font-size: 1.1em;
            line-height: 1.8;
            color: #444;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .scores-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .score-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: transform 0.2s;
        }
        
        .score-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .score-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
        }
        
        .score-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .score-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            margin-top: 10px;
            overflow: hidden;
        }
        
        .score-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .career-matches {
            margin-top: 20px;
        }
        
        .career-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }
        
        .career-name {
            font-weight: bold;
            color: #333;
        }
        
        .career-match {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        
        .recommendations {
            margin-top: 20px;
        }
        
        .rec-category {
            margin-bottom: 20px;
        }
        
        .rec-category h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .rec-list {
            list-style: none;
            padding: 0;
        }
        
        .rec-list li {
            background: #f8f9fa;
            padding: 10px 15px;
            margin-bottom: 5px;
            border-radius: 5px;
            border-left: 3px solid #667eea;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
        }
        
        .cognitive-score {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .cognitive-score .score {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        @media print {
            body {
                background: white;
                font-size: 12px;
            }
            
            .section {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Psychometric Assessment Report</h1>
        <div class="subtitle">Comprehensive Personality & Career Assessment</div>
    </div>

    <div class="student-info">
        <h2>Student Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Name:</div>
                <div>{{ student_name }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Grade:</div>
                <div>{{ grade }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Test Date:</div>
                <div>{{ test_date }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Report Generated:</div>
                <div>{{ report_generated }}</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>Executive Summary</h2>
        <div class="summary-text">
            {{ ai_summary }}
        </div>
    </div>

    {% if riasec_scores %}
    <div class="section">
        <h2>RIASEC Career Interest Profile</h2>
        <p>Your interests align with the following career personality types:</p>
        <div class="scores-grid">
            {% for trait in riasec_traits %}
            <div class="score-card">
                <div class="score-label">{{ trait }}</div>
                <div class="score-value">{{ riasec_scores.get(trait, 0) }}</div>
                <div class="score-bar">
                    <div class="score-fill" style="width: {{ (riasec_scores.get(trait, 0) / 5 * 100) }}%"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if big5_scores %}
    <div class="section">
        <h2>Big Five Personality Traits</h2>
        <p>Your personality profile across the five major dimensions:</p>
        <div class="scores-grid">
            {% for trait in big5_traits %}
            <div class="score-card">
                <div class="score-label">{{ trait }}</div>
                <div class="score-value">{{ "%.1f"|format(big5_scores.get(trait, 0)) }}</div>
                <div class="score-bar">
                    <div class="score-fill" style="width: {{ (big5_scores.get(trait, 0) / 5 * 100) }}%"></div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    {% if cognitive_score %}
    <div class="section">
        <h2>Cognitive Ability Assessment</h2>
        <div class="cognitive-score">
            <div class="score">{{ "%.0f"|format(cognitive_score) }}%</div>
            <div>Overall Cognitive Performance</div>
        </div>
        <p>This score represents your performance on reasoning, problem-solving, and analytical thinking tasks.</p>
    </div>
    {% endif %}

    {% if pe_fit %}
    <div class="section">
        <h2>Career Cluster Matches</h2>
        <p>Based on your RIASEC profile, here are your top career cluster matches:</p>
        <div class="career-matches">
            {% for career, match_score in pe_fit %}
            <div class="career-item">
                <div class="career-name">{{ career }}</div>
                <div class="career-match">{{ "%.0f"|format(match_score * 100) }}% Match</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>This report is generated by the Psychometric Assessment Platform</p>
        <p>Results are based on your responses and should be used as guidance for personal and academic development</p>
    </div>
</body>
</html>
