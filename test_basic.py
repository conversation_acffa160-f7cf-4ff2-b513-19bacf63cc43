#!/usr/bin/env python3
"""
Basic test script to verify the psychometric platform setup
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🧪 Testing module imports...")
    
    # Test basic imports
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ Google Generative AI imported successfully")
    except ImportError as e:
        print(f"❌ Google Generative AI import failed: {e}")
        return False
    
    try:
        from config.settings import settings
        print("✅ Settings imported successfully")
        print(f"   - Gemini API Key configured: {'Yes' if settings.GEMINI_API_KEY else 'No'}")
    except ImportError as e:
        print(f"❌ Settings import failed: {e}")
        return False
    
    # Test AI module imports
    try:
        from ai_module.question_gen import generate_question_batch
        print("✅ Question generation module imported successfully")
    except ImportError as e:
        print(f"❌ Question generation import failed: {e}")
        return False
    
    try:
        from ai_module.scoring import score_riasec, score_big5, score_cognitive
        print("✅ Scoring modules imported successfully")
    except ImportError as e:
        print(f"❌ Scoring modules import failed: {e}")
        return False
    
    return True

def test_question_generation():
    """Test question generation functionality"""
    print("\n📝 Testing question generation...")
    
    try:
        from ai_module.question_gen import generate_question_batch
        from config.settings import settings
        
        if not settings.GEMINI_API_KEY:
            print("❌ Gemini API key not configured")
            return False
        
        # Test with a simple request
        questions = generate_question_batch(
            grade=10,
            theory="RIASEC",
            trait="Realistic",
            difficulty="easy",
            num_questions=1
        )
        
        if questions and len(questions) > 0:
            print(f"✅ Generated {len(questions)} question(s)")
            print(f"   Sample question: {questions[0]['question'][:50]}...")
            return True
        else:
            print("❌ No questions generated")
            return False
            
    except Exception as e:
        print(f"❌ Question generation failed: {e}")
        return False

def test_scoring():
    """Test scoring functionality"""
    print("\n📊 Testing scoring functionality...")
    
    try:
        from ai_module.scoring import score_riasec, score_big5, score_cognitive
        
        # Test RIASEC scoring
        sample_responses = [
            {
                "question_id": "test1",
                "theory": "RIASEC",
                "trait": "Realistic",
                "selected_option": 0
            }
        ]
        
        riasec_scores = score_riasec(sample_responses)
        print(f"✅ RIASEC scoring works: {riasec_scores}")
        
        # Test Big5 scoring
        big5_responses = [
            {
                "question_id": "test2",
                "theory": "Big5",
                "trait": "Openness",
                "selected_option": 2
            }
        ]
        
        big5_scores = score_big5(big5_responses)
        print(f"✅ Big5 scoring works: {big5_scores}")
        
        # Test Cognitive scoring
        cognitive_responses = [
            {
                "question_id": "test3",
                "theory": "Cognitive",
                "trait": "Verbal",
                "selected_option": 1,
                "correct_index": 1
            }
        ]
        
        cognitive_score = score_cognitive(cognitive_responses)
        print(f"✅ Cognitive scoring works: {cognitive_score}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Scoring test failed: {e}")
        return False

def test_api_endpoints():
    """Test API endpoints"""
    print("\n🌐 Testing API endpoints...")
    
    try:
        import requests
        
        # Test health endpoint
        response = requests.get("http://localhost:8001/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
            data = response.json()
            print(f"   - Gemini configured: {data['environment']['gemini_configured']}")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False
        
        # Test root endpoint
        response = requests.get("http://localhost:8001/")
        if response.status_code == 200:
            print("✅ Root endpoint working")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API endpoint test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Psychometric Testing Platform - Basic Tests")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Module Imports", test_imports),
        ("Question Generation", test_question_generation),
        ("Scoring Functions", test_scoring),
        ("API Endpoints", test_api_endpoints)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Your psychometric platform is ready to use.")
        print("\n📖 Next steps:")
        print("   1. Visit http://localhost:8001/docs for API documentation")
        print("   2. Try the demo: python3 demo_workflow.py")
        print("   3. Create test sessions and generate questions")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
