{"question_schema": {"type": "object", "required": ["grade", "theory", "trait", "question", "options", "correct_index", "difficulty"], "properties": {"grade": {"type": "integer", "minimum": 1, "maximum": 12, "description": "Student grade level"}, "theory": {"type": "string", "enum": ["RIASEC", "Big5", "Cognitive", "<PERSON>", "CPI"], "description": "Psychometric theory"}, "trait": {"type": "string", "description": "Specific trait within the theory"}, "question": {"type": "string", "minLength": 10, "description": "The question text"}, "options": {"type": "array", "items": {"type": "string", "minLength": 1}, "minItems": 4, "maxItems": 4, "description": "Four answer options"}, "correct_index": {"type": "integer", "minimum": 0, "maximum": 3, "description": "Index of correct answer (0-3)"}, "difficulty": {"type": "string", "enum": ["easy", "medium", "hard"], "description": "Question difficulty level"}}}, "response_schema": {"type": "object", "required": ["question_id", "theory", "trait", "selected_option"], "properties": {"question_id": {"type": "string", "description": "Unique identifier for the question"}, "theory": {"type": "string", "enum": ["RIASEC", "Big5", "Cognitive", "<PERSON>", "CPI"], "description": "Psychometric theory"}, "trait": {"type": "string", "description": "Specific trait within the theory"}, "selected_option": {"type": "integer", "minimum": 0, "maximum": 3, "description": "Index of selected answer (0-3)"}, "correct_index": {"type": "integer", "minimum": 0, "maximum": 3, "description": "Index of correct answer (for cognitive questions)"}, "time_taken": {"type": "number", "minimum": 0, "description": "Time taken to answer in seconds (optional)"}}}, "trait_mappings": {"RIASEC": ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"], "Big5": ["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"], "Cognitive": ["Verbal", "Numerical", "Spatial", "Logical"], "Hogan": ["Adjustment", "Ambition", "Sociability", "Interpersonal_Sensitivity", "Prudence", "Inquisitive", "Learning_Approach"], "CPI": ["Dominance", "Capacity_for_Status", "Sociability", "Social_Presence", "Self_Acceptance", "Independence", "Empathy"]}, "difficulty_distribution": {"easy": 0.4, "medium": 0.4, "hard": 0.2}, "questions_per_grade_theory": 60}