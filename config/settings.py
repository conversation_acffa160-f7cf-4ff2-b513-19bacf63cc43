# config/settings.py
import os
from dotenv import load_dotenv

load_dotenv()

class Settings:
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./psychometric.db")
    SCHOLARSHIPS_API_URL = os.getenv("SCHOLARSHIPS_API_URL", "https://api.example.com/scholarships")
    LOANS_API_URL = os.getenv("LOANS_API_URL", "https://api.example.com/loans")
    REPORTS_DIR = os.getenv("REPORTS_DIR", "./static/reports")
    BASE_URL = os.getenv("BASE_URL", "http://localhost:8000")
    DEBUG = os.getenv("DEBUG", "True").lower() == "true"
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    
    # Psychometric Test Configuration
    THEORIES = ["RIASEC", "Big5", "Cognitive", "Hogan", "CPI"]
    RIASEC_TRAITS = ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
    BIG5_TRAITS = ["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
    COGNITIVE_TRAITS = ["Verbal", "Numerical", "Spatial", "Logical"]
    HOGAN_TRAITS = ["Adjustment", "Ambition", "Sociability", "Interpersonal_Sensitivity", "Prudence", "Inquisitive", "Learning_Approach"]
    CPI_TRAITS = ["Dominance", "Capacity_for_Status", "Sociability", "Social_Presence", "Self_Acceptance", "Independence", "Empathy"]
    
    DIFFICULTY_LEVELS = ["easy", "medium", "hard"]
    QUESTIONS_PER_GRADE_THEORY = 60

settings = Settings()
