# demo_workflow.py
"""
Demonstration script showing the complete psychometric testing workflow
"""
import asyncio
import json
import os
from datetime import datetime
from ai_module import (
    generate_question_batch,
    score_riasec,
    score_big5,
    score_cognitive,
    compute_pe_fit,
    render_report,
    fetch_scholarships
)

async def demo_complete_workflow():
    """Demonstrate the complete psychometric testing workflow"""
    
    print("🧠 Psychometric Testing Platform - Complete Workflow Demo")
    print("=" * 60)
    
    # Student information
    student_name = "<PERSON> Johnson"
    grade = 10
    
    print(f"\n👤 Student: {student_name}, Grade: {grade}")
    
    # Step 1: Generate Questions
    print("\n📝 Step 1: Generating Questions...")
    
    try:
        # Generate RIASEC questions
        riasec_questions = generate_question_batch(
            grade=grade,
            theory="RIASEC",
            trait="Realistic",
            difficulty="medium",
            num_questions=3
        )
        print(f"✅ Generated {len(riasec_questions)} RIASEC questions")
        
        # Generate Big5 questions
        big5_questions = generate_question_batch(
            grade=grade,
            theory="Big5",
            trait="Openness",
            difficulty="medium",
            num_questions=3
        )
        print(f"✅ Generated {len(big5_questions)} Big5 questions")
        
        # Generate Cognitive questions
        cognitive_questions = generate_question_batch(
            grade=grade,
            theory="Cognitive",
            trait="Verbal",
            difficulty="medium",
            num_questions=3
        )
        print(f"✅ Generated {len(cognitive_questions)} Cognitive questions")
        
    except Exception as e:
        print(f"❌ Question generation failed: {e}")
        print("📝 Using sample questions for demo...")
        
        # Use sample questions if generation fails
        riasec_questions = [
            {
                "grade": grade,
                "theory": "RIASEC",
                "trait": "Realistic",
                "question": "Which activity would you most enjoy?",
                "options": ["Building things", "Reading books", "Painting", "Teaching others"],
                "correct_index": 0,
                "difficulty": "medium"
            }
        ]
        
        big5_questions = [
            {
                "grade": grade,
                "theory": "Big5",
                "trait": "Openness",
                "question": "How do you typically approach new experiences?",
                "options": ["Very cautiously", "Somewhat cautiously", "With interest", "With great enthusiasm"],
                "correct_index": 3,
                "difficulty": "medium"
            }
        ]
        
        cognitive_questions = [
            {
                "grade": grade,
                "theory": "Cognitive",
                "trait": "Verbal",
                "question": "What is the opposite of 'abundant'?",
                "options": ["Scarce", "Plenty", "Large", "Small"],
                "correct_index": 0,
                "difficulty": "medium"
            }
        ]
    
    # Step 2: Simulate Student Responses
    print("\n🎯 Step 2: Simulating Student Responses...")
    
    sample_responses = [
        # RIASEC responses
        {
            "question_id": "riasec_1",
            "theory": "RIASEC",
            "trait": "Realistic",
            "selected_option": 0
        },
        {
            "question_id": "riasec_2",
            "theory": "RIASEC",
            "trait": "Investigative",
            "selected_option": 1
        },
        {
            "question_id": "riasec_3",
            "theory": "RIASEC",
            "trait": "Artistic",
            "selected_option": 2
        },
        
        # Big5 responses
        {
            "question_id": "big5_1",
            "theory": "Big5",
            "trait": "Openness",
            "selected_option": 3
        },
        {
            "question_id": "big5_2",
            "theory": "Big5",
            "trait": "Conscientiousness",
            "selected_option": 2
        },
        {
            "question_id": "big5_3",
            "theory": "Big5",
            "trait": "Extraversion",
            "selected_option": 1
        },
        
        # Cognitive responses
        {
            "question_id": "cognitive_1",
            "theory": "Cognitive",
            "trait": "Verbal",
            "selected_option": 0,
            "correct_index": 0
        },
        {
            "question_id": "cognitive_2",
            "theory": "Cognitive",
            "trait": "Numerical",
            "selected_option": 2,
            "correct_index": 1
        },
        {
            "question_id": "cognitive_3",
            "theory": "Cognitive",
            "trait": "Spatial",
            "selected_option": 1,
            "correct_index": 1
        }
    ]
    
    print(f"✅ Simulated {len(sample_responses)} student responses")
    
    # Step 3: Calculate Scores
    print("\n📊 Step 3: Calculating Scores...")
    
    # Calculate RIASEC scores
    riasec_responses = [r for r in sample_responses if r["theory"] == "RIASEC"]
    riasec_scores = score_riasec(riasec_responses)
    print(f"✅ RIASEC Scores: {riasec_scores}")
    
    # Calculate Big5 scores
    big5_responses = [r for r in sample_responses if r["theory"] == "Big5"]
    big5_scores = score_big5(big5_responses)
    print(f"✅ Big5 Scores: {big5_scores}")
    
    # Calculate Cognitive score
    cognitive_responses = [r for r in sample_responses if r["theory"] == "Cognitive"]
    cognitive_score = score_cognitive(cognitive_responses)
    print(f"✅ Cognitive Score: {cognitive_score:.1f}%")
    
    # Calculate Person-Environment fit
    pe_fit = compute_pe_fit(riasec_scores)
    print(f"✅ Top Career Matches: {pe_fit}")
    
    # Step 4: Generate Report
    print("\n📄 Step 4: Generating Report...")
    
    report_data = {
        "student_name": student_name,
        "grade": grade,
        "riasec_scores": riasec_scores,
        "big5_scores": big5_scores,
        "cognitive_score": cognitive_score,
        "pe_fit": pe_fit,
        "test_date": datetime.now().strftime("%B %d, %Y")
    }
    
    # Ensure reports directory exists
    os.makedirs("static/reports", exist_ok=True)
    
    report_filename = f"demo_report_{student_name.replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
    report_path = os.path.join("static/reports", report_filename)
    
    try:
        generated_report = render_report(report_data, report_path)
        print(f"✅ Report generated: {generated_report}")
    except Exception as e:
        print(f"❌ Report generation failed: {e}")
        print("📄 Report data prepared successfully anyway")
    
    # Step 5: Fetch External Data
    print("\n🌐 Step 5: Fetching External Data...")
    
    try:
        scholarships = await fetch_scholarships(grade=grade, limit=5)
        print(f"✅ Found {len(scholarships)} scholarships for grade {grade}")
        
        if scholarships:
            print("📚 Sample scholarships:")
            for i, scholarship in enumerate(scholarships[:3], 1):
                print(f"   {i}. {scholarship['name']} - ${scholarship['amount']:,.0f}")
    
    except Exception as e:
        print(f"❌ External data fetch failed: {e}")
    
    # Step 6: Summary
    print("\n🎉 Step 6: Workflow Summary")
    print("=" * 60)
    print(f"Student: {student_name} (Grade {grade})")
    print(f"Questions Generated: ✅")
    print(f"Responses Processed: {len(sample_responses)} responses")
    print(f"Scores Calculated: ✅")
    print(f"Report Generated: ✅")
    print(f"External Data: ✅")
    
    print("\n📈 Key Results:")
    print(f"   • Top RIASEC Interest: {max(riasec_scores, key=riasec_scores.get)}")
    print(f"   • Cognitive Performance: {cognitive_score:.1f}%")
    print(f"   • Best Career Match: {pe_fit[0][0]} ({pe_fit[0][1]:.1%} match)")
    
    print("\n✨ Workflow completed successfully!")
    
    return {
        "student_name": student_name,
        "grade": grade,
        "scores": {
            "riasec": riasec_scores,
            "big5": big5_scores,
            "cognitive": cognitive_score
        },
        "career_matches": pe_fit,
        "report_path": report_path if 'generated_report' in locals() else None
    }

def demo_individual_functions():
    """Demonstrate individual function capabilities"""
    
    print("\n🔧 Individual Function Demonstrations")
    print("=" * 60)
    
    # Demo question validation
    print("\n1. Question Schema Validation:")
    from ai_module.question_gen import validate_question_schema
    
    valid_question = {
        "grade": 9,
        "theory": "RIASEC",
        "trait": "Realistic",
        "question": "Which activity appeals to you most?",
        "options": ["Building", "Reading", "Drawing", "Talking"],
        "correct_index": 0,
        "difficulty": "easy"
    }
    
    print(f"   Valid question: {validate_question_schema(valid_question)}")
    
    # Demo scoring with edge cases
    print("\n2. Scoring Edge Cases:")
    empty_responses = []
    empty_riasec = score_riasec(empty_responses)
    print(f"   Empty RIASEC scores: {empty_riasec}")
    
    # Demo report data preparation
    print("\n3. Report Data Preparation:")
    from ai_module.report_gen import generate_fallback_summary
    
    sample_data = {
        "student_name": "Test Student",
        "grade": 8,
        "riasec_scores": {"Realistic": 3, "Investigative": 4},
        "big5_scores": {"Openness": 3.5, "Conscientiousness": 4.0},
        "cognitive_score": 75.0
    }
    
    fallback_summary = generate_fallback_summary(sample_data)
    print(f"   Fallback summary length: {len(fallback_summary)} characters")
    
    print("\n✅ Individual function demos completed!")

async def main():
    """Main demo function"""
    try:
        # Run complete workflow demo
        workflow_result = await demo_complete_workflow()
        
        # Run individual function demos
        demo_individual_functions()
        
        print(f"\n🏁 Demo completed successfully!")
        print(f"📊 Results saved for: {workflow_result['student_name']}")
        
        return workflow_result
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Run the demo
    result = asyncio.run(main())
