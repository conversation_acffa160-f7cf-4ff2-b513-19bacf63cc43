# ai_module/data_ingestion.py
import asyncio
import logging
from typing import List, Dict, Any, Optional
import httpx
import json
from datetime import datetime, timedelta
from sentence_transformers import SentenceTransformer
import numpy as np
from config.settings import settings

logger = logging.getLogger(__name__)

# Initialize sentence transformer for embeddings (lazy loading)
_embedding_model = None

def get_embedding_model():
    """Lazy load the sentence transformer model"""
    global _embedding_model
    if _embedding_model is None:
        _embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
    return _embedding_model


async def fetch_scholarships(grade: Optional[int] = None, limit: int = 50) -> List[Dict[str, Any]]:
    """
    Fetch scholarship data from external API
    
    Args:
        grade: Filter by grade level (optional)
        limit: Maximum number of scholarships to return
    
    Returns:
        List of normalized scholarship dictionaries
    """
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # Example API call - replace with actual scholarship API
            params = {"limit": limit}
            if grade:
                params["grade"] = grade
            
            response = await client.get(settings.SCHOLARSHIPS_API_URL, params=params)
            response.raise_for_status()
            
            raw_data = response.json()
            
            # Normalize the data structure
            scholarships = []
            for item in raw_data.get("scholarships", []):
                normalized = normalize_scholarship_data(item)
                if normalized:
                    scholarships.append(normalized)
            
            logger.info(f"Fetched {len(scholarships)} scholarships")
            return scholarships
            
    except httpx.RequestError as e:
        logger.error(f"Failed to fetch scholarships: {e}")
        return get_sample_scholarships(grade, limit)
    
    except Exception as e:
        logger.error(f"Unexpected error fetching scholarships: {e}")
        return get_sample_scholarships(grade, limit)


async def fetch_loans(grade: Optional[int] = None, limit: int = 20) -> List[Dict[str, Any]]:
    """
    Fetch loan/financial aid data from external API
    
    Args:
        grade: Filter by grade level (optional)
        limit: Maximum number of loans to return
    
    Returns:
        List of normalized loan dictionaries
    """
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            params = {"limit": limit}
            if grade:
                params["grade"] = grade
            
            response = await client.get(settings.LOANS_API_URL, params=params)
            response.raise_for_status()
            
            raw_data = response.json()
            
            # Normalize the data structure
            loans = []
            for item in raw_data.get("loans", []):
                normalized = normalize_loan_data(item)
                if normalized:
                    loans.append(normalized)
            
            logger.info(f"Fetched {len(loans)} loans")
            return loans
            
    except httpx.RequestError as e:
        logger.error(f"Failed to fetch loans: {e}")
        return get_sample_loans(grade, limit)
    
    except Exception as e:
        logger.error(f"Unexpected error fetching loans: {e}")
        return get_sample_loans(grade, limit)


def normalize_scholarship_data(raw_item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Normalize scholarship data from various API formats
    
    Args:
        raw_item: Raw scholarship data from API
    
    Returns:
        Normalized scholarship dictionary or None if invalid
    """
    
    try:
        # Handle different possible API response formats
        normalized = {
            "id": raw_item.get("id") or raw_item.get("scholarship_id"),
            "name": raw_item.get("name") or raw_item.get("title") or raw_item.get("scholarship_name"),
            "description": raw_item.get("description") or raw_item.get("summary", ""),
            "amount": parse_amount(raw_item.get("amount") or raw_item.get("value")),
            "deadline": parse_date(raw_item.get("deadline") or raw_item.get("application_deadline")),
            "eligibility": raw_item.get("eligibility") or raw_item.get("requirements", []),
            "grade_levels": parse_grade_levels(raw_item.get("grade_levels") or raw_item.get("eligible_grades")),
            "category": raw_item.get("category") or "General",
            "provider": raw_item.get("provider") or raw_item.get("organization", "Unknown"),
            "url": raw_item.get("url") or raw_item.get("application_url"),
            "renewable": raw_item.get("renewable", False),
            "need_based": raw_item.get("need_based", False),
            "merit_based": raw_item.get("merit_based", True)
        }
        
        # Validate required fields
        if not normalized["name"] or not normalized["amount"]:
            return None
        
        return normalized
        
    except Exception as e:
        logger.warning(f"Failed to normalize scholarship data: {e}")
        return None


def normalize_loan_data(raw_item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Normalize loan data from various API formats
    
    Args:
        raw_item: Raw loan data from API
    
    Returns:
        Normalized loan dictionary or None if invalid
    """
    
    try:
        normalized = {
            "id": raw_item.get("id") or raw_item.get("loan_id"),
            "name": raw_item.get("name") or raw_item.get("title") or raw_item.get("loan_name"),
            "description": raw_item.get("description") or raw_item.get("summary", ""),
            "max_amount": parse_amount(raw_item.get("max_amount") or raw_item.get("maximum")),
            "interest_rate": raw_item.get("interest_rate") or raw_item.get("rate"),
            "term_years": raw_item.get("term_years") or raw_item.get("repayment_term"),
            "eligibility": raw_item.get("eligibility") or raw_item.get("requirements", []),
            "grade_levels": parse_grade_levels(raw_item.get("grade_levels") or raw_item.get("eligible_grades")),
            "provider": raw_item.get("provider") or raw_item.get("lender", "Unknown"),
            "url": raw_item.get("url") or raw_item.get("application_url"),
            "federal": raw_item.get("federal", False),
            "subsidized": raw_item.get("subsidized", False),
            "credit_check": raw_item.get("credit_check", True)
        }
        
        # Validate required fields
        if not normalized["name"] or not normalized["max_amount"]:
            return None
        
        return normalized
        
    except Exception as e:
        logger.warning(f"Failed to normalize loan data: {e}")
        return None


def parse_amount(amount_str: Any) -> Optional[float]:
    """Parse amount string to float"""
    if not amount_str:
        return None
    
    if isinstance(amount_str, (int, float)):
        return float(amount_str)
    
    if isinstance(amount_str, str):
        # Remove currency symbols and commas
        cleaned = amount_str.replace("$", "").replace(",", "").strip()
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    return None


def parse_date(date_str: Any) -> Optional[str]:
    """Parse date string to ISO format"""
    if not date_str:
        return None
    
    if isinstance(date_str, str):
        try:
            # Try to parse common date formats
            for fmt in ["%Y-%m-%d", "%m/%d/%Y", "%d/%m/%Y", "%B %d, %Y"]:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return dt.strftime("%Y-%m-%d")
                except ValueError:
                    continue
        except Exception:
            pass
    
    return date_str


def parse_grade_levels(grades: Any) -> List[int]:
    """Parse grade levels from various formats"""
    if not grades:
        return []
    
    if isinstance(grades, list):
        result = []
        for grade in grades:
            if isinstance(grade, int) and 1 <= grade <= 12:
                result.append(grade)
            elif isinstance(grade, str) and grade.isdigit():
                g = int(grade)
                if 1 <= g <= 12:
                    result.append(g)
        return result
    
    if isinstance(grades, str):
        # Handle ranges like "9-12" or comma-separated "9,10,11,12"
        if "-" in grades:
            try:
                start, end = grades.split("-")
                return list(range(int(start), int(end) + 1))
            except ValueError:
                pass
        
        if "," in grades:
            try:
                return [int(g.strip()) for g in grades.split(",") if g.strip().isdigit()]
            except ValueError:
                pass
    
    return []


def get_sample_scholarships(grade: Optional[int] = None, limit: int = 50) -> List[Dict[str, Any]]:
    """Return sample scholarship data when API is unavailable"""
    
    sample_scholarships = [
        {
            "id": "sch_001",
            "name": "Academic Excellence Scholarship",
            "description": "Merit-based scholarship for high-achieving students",
            "amount": 5000.0,
            "deadline": "2024-03-15",
            "eligibility": ["GPA 3.5+", "Full-time enrollment"],
            "grade_levels": [9, 10, 11, 12],
            "category": "Academic",
            "provider": "Education Foundation",
            "url": "https://example.com/scholarship1",
            "renewable": True,
            "need_based": False,
            "merit_based": True
        },
        {
            "id": "sch_002",
            "name": "STEM Future Leaders",
            "description": "Supporting students pursuing STEM careers",
            "amount": 7500.0,
            "deadline": "2024-04-01",
            "eligibility": ["STEM major", "Community service"],
            "grade_levels": [11, 12],
            "category": "STEM",
            "provider": "Tech Foundation",
            "url": "https://example.com/scholarship2",
            "renewable": False,
            "need_based": False,
            "merit_based": True
        },
        {
            "id": "sch_003",
            "name": "Community Service Award",
            "description": "Recognizing students who give back to their community",
            "amount": 3000.0,
            "deadline": "2024-05-01",
            "eligibility": ["100+ volunteer hours", "Essay required"],
            "grade_levels": [9, 10, 11, 12],
            "category": "Service",
            "provider": "Community Foundation",
            "url": "https://example.com/scholarship3",
            "renewable": True,
            "need_based": True,
            "merit_based": False
        }
    ]
    
    # Filter by grade if specified
    if grade:
        sample_scholarships = [s for s in sample_scholarships if grade in s["grade_levels"]]
    
    return sample_scholarships[:limit]


def get_sample_loans(grade: Optional[int] = None, limit: int = 20) -> List[Dict[str, Any]]:
    """Return sample loan data when API is unavailable"""
    
    sample_loans = [
        {
            "id": "loan_001",
            "name": "Federal Direct Subsidized Loan",
            "description": "Low-interest federal loan for undergraduate students",
            "max_amount": 5500.0,
            "interest_rate": 5.5,
            "term_years": 10,
            "eligibility": ["Financial need", "Enrolled at least half-time"],
            "grade_levels": [9, 10, 11, 12],
            "provider": "U.S. Department of Education",
            "url": "https://studentaid.gov",
            "federal": True,
            "subsidized": True,
            "credit_check": False
        },
        {
            "id": "loan_002",
            "name": "Private Student Loan",
            "description": "Competitive rates for creditworthy borrowers",
            "max_amount": 25000.0,
            "interest_rate": 7.2,
            "term_years": 15,
            "eligibility": ["Credit check required", "Cosigner may be needed"],
            "grade_levels": [11, 12],
            "provider": "Education Lending Corp",
            "url": "https://example.com/loan2",
            "federal": False,
            "subsidized": False,
            "credit_check": True
        }
    ]
    
    # Filter by grade if specified
    if grade:
        sample_loans = [l for l in sample_loans if grade in l["grade_levels"]]
    
    return sample_loans[:limit]


async def create_embeddings(texts: List[str]) -> np.ndarray:
    """
    Create embeddings for text data using sentence transformers
    
    Args:
        texts: List of text strings to embed
    
    Returns:
        NumPy array of embeddings
    """
    
    try:
        model = get_embedding_model()
        embeddings = model.encode(texts)
        return embeddings
    
    except Exception as e:
        logger.error(f"Failed to create embeddings: {e}")
        # Return zero embeddings as fallback
        return np.zeros((len(texts), 384))  # MiniLM embedding dimension


async def store_embeddings_pseudo(data: List[Dict[str, Any]], embedding_type: str):
    """
    Pseudo-code for storing embeddings in a vector database
    
    Args:
        data: List of scholarship/loan data with embeddings
        embedding_type: "scholarships" or "loans"
    """
    
    # This is pseudo-code - replace with actual vector DB implementation
    logger.info(f"Storing {len(data)} {embedding_type} embeddings")
    
    # Example with a hypothetical vector database:
    # vector_db = VectorDatabase()
    # for item in data:
    #     vector_db.insert(
    #         id=item["id"],
    #         vector=item["embedding"],
    #         metadata=item,
    #         collection=embedding_type
    #     )
    
    # For now, just log the action
    logger.info(f"Would store embeddings for {embedding_type} in vector database")
