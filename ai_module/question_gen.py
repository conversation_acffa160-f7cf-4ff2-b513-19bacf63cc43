# ai_module/question_gen.py
import json
import logging
from typing import List, Dict, Any
import google.generativeai as genai
from config.settings import settings

# Configure Gemini
genai.configure(api_key=settings.GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-2.0-flash-exp')

logger = logging.getLogger(__name__)

def generate_question_batch(grade: int, theory: str, trait: str, difficulty: str, num_questions: int) -> List[Dict[str, Any]]:
    """
    Generate a batch of psychometric questions using Gemini 2.5 Flash
    
    Args:
        grade: Student grade level (1-12)
        theory: Psychometric theory (RIASEC, Big5, Cognitive, Hogan, CPI)
        trait: Specific trait within the theory
        difficulty: Question difficulty (easy, medium, hard)
        num_questions: Number of questions to generate
    
    Returns:
        List of question dictionaries with schema:
        {
            "grade": int,
            "theory": str,
            "trait": str,
            "question": str,
            "options": [str, str, str, str],
            "correct_index": int,
            "difficulty": str
        }
    """
    
    prompt = f"""
    Generate {num_questions} multiple-choice psychometric questions for:
    - Grade Level: {grade}
    - Theory: {theory}
    - Trait: {trait}
    - Difficulty: {difficulty}
    
    Requirements:
    1. Questions must be age-appropriate for grade {grade}
    2. Each question should assess the {trait} trait within {theory} theory
    3. Provide exactly 4 options per question
    4. Questions should be scenario-based and relatable to students
    5. Difficulty level should be {difficulty}
    
    Return ONLY a valid JSON array with this exact schema:
    [
        {{
            "grade": {grade},
            "theory": "{theory}",
            "trait": "{trait}",
            "question": "Question text here",
            "options": ["Option A", "Option B", "Option C", "Option D"],
            "correct_index": 0,
            "difficulty": "{difficulty}"
        }}
    ]
    
    Theory-specific guidelines:
    - RIASEC: Focus on career interests and preferences
    - Big5: Assess personality dimensions
    - Cognitive: Test reasoning and problem-solving abilities
    - Hogan: Evaluate workplace personality
    - CPI: Measure interpersonal behavior and social interaction
    
    Make questions engaging and relevant to grade {grade} students.
    """
    
    try:
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        # Clean up response text to extract JSON
        if response_text.startswith("```json"):
            response_text = response_text[7:]
        if response_text.endswith("```"):
            response_text = response_text[:-3]
        
        questions = json.loads(response_text)
        
        # Validate the response
        if not isinstance(questions, list):
            raise ValueError("Response is not a list")
        
        for i, question in enumerate(questions):
            required_fields = ["grade", "theory", "trait", "question", "options", "correct_index", "difficulty"]
            for field in required_fields:
                if field not in question:
                    raise ValueError(f"Missing field '{field}' in question {i}")
            
            if len(question["options"]) != 4:
                raise ValueError(f"Question {i} must have exactly 4 options")
            
            if not (0 <= question["correct_index"] <= 3):
                raise ValueError(f"Question {i} correct_index must be 0-3")
        
        logger.info(f"Successfully generated {len(questions)} questions for {theory}-{trait} grade {grade}")
        return questions
        
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response: {e}")
        logger.error(f"Response text: {response_text}")
        raise ValueError(f"Invalid JSON response from Gemini: {e}")
    
    except Exception as e:
        logger.error(f"Error generating questions: {e}")
        raise ValueError(f"Failed to generate questions: {e}")


def validate_question_schema(question: Dict[str, Any]) -> bool:
    """Validate a single question against the expected schema"""
    required_fields = ["grade", "theory", "trait", "question", "options", "correct_index", "difficulty"]
    
    for field in required_fields:
        if field not in question:
            return False
    
    if not isinstance(question["options"], list) or len(question["options"]) != 4:
        return False
    
    if not isinstance(question["correct_index"], int) or not (0 <= question["correct_index"] <= 3):
        return False
    
    return True


def generate_full_question_bank(grades: List[int] = None) -> Dict[str, List[Dict]]:
    """
    Generate complete question bank for all grades and theories
    
    Args:
        grades: List of grades to generate for (default: 1-12)
    
    Returns:
        Dictionary with structure: {f"{grade}_{theory}_{trait}": [questions]}
    """
    if grades is None:
        grades = list(range(1, 13))
    
    question_bank = {}
    
    theory_traits = {
        "RIASEC": settings.RIASEC_TRAITS,
        "Big5": settings.BIG5_TRAITS,
        "Cognitive": settings.COGNITIVE_TRAITS,
        "Hogan": settings.HOGAN_TRAITS,
        "CPI": settings.CPI_TRAITS
    }
    
    for grade in grades:
        for theory, traits in theory_traits.items():
            for trait in traits:
                questions_per_difficulty = settings.QUESTIONS_PER_GRADE_THEORY // 3
                
                for difficulty in settings.DIFFICULTY_LEVELS:
                    try:
                        questions = generate_question_batch(
                            grade=grade,
                            theory=theory,
                            trait=trait,
                            difficulty=difficulty,
                            num_questions=questions_per_difficulty
                        )
                        
                        key = f"{grade}_{theory}_{trait}_{difficulty}"
                        question_bank[key] = questions
                        
                    except Exception as e:
                        logger.error(f"Failed to generate questions for {key}: {e}")
                        question_bank[key] = []
    
    return question_bank
