# ai_module/report_gen.py
import os
import logging
from datetime import datetime
from typing import Dict, Any
import google.generativeai as genai
from jinja2 import Environment, FileSystemLoader
from weasyprint import HTML, CSS
from config.settings import settings

logger = logging.getLogger(__name__)

# Configure Gemini for report polishing
genai.configure(api_key=settings.GEMINI_API_KEY)
model = genai.GenerativeModel('gemini-2.0-flash-exp')

def polish_summary_with_ai(raw_data: Dict[str, Any]) -> str:
    """
    Use Gemini to create a friendly, narrative summary of psychometric results
    
    Args:
        raw_data: Dictionary containing all psychometric scores and student info
    
    Returns:
        Polished narrative summary as a string
    """
    
    prompt = f"""
    Create a friendly, encouraging narrative summary for a student's psychometric test results.
    
    Student Information:
    - Name: {raw_data.get('student_name', 'Student')}
    - Grade: {raw_data.get('grade', 'N/A')}
    
    Test Results:
    - RIASEC Scores: {raw_data.get('riasec_scores', {})}
    - Big Five Scores: {raw_data.get('big5_scores', {})}
    - Cognitive Score: {raw_data.get('cognitive_score', 'N/A')}
    - Career Matches: {raw_data.get('pe_fit', [])}
    
    Requirements:
    1. Write in a warm, encouraging tone appropriate for the student's grade level
    2. Explain what the scores mean in simple terms
    3. Highlight the student's strengths
    4. Provide actionable insights for personal development
    5. Connect results to potential career paths
    6. Keep it positive and motivating
    7. Length: 200-300 words
    
    Focus on helping the student understand their unique profile and potential.
    """
    
    try:
        response = model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        logger.error(f"Failed to generate AI summary: {e}")
        return generate_fallback_summary(raw_data)


def generate_fallback_summary(data: Dict[str, Any]) -> str:
    """Generate a basic summary if AI polishing fails"""
    name = data.get('student_name', 'Student')
    grade = data.get('grade', 'N/A')
    
    summary = f"""
    Dear {name},
    
    Congratulations on completing your psychometric assessment! Your results show a unique 
    profile of interests, personality traits, and cognitive abilities.
    
    Your strongest career interest areas align with your personality and cognitive strengths, 
    suggesting several exciting career paths to explore. These results can help guide your 
    academic choices and future career planning.
    
    Remember, these results represent your current preferences and abilities. Continue to 
    explore new interests and develop your skills as you grow and learn.
    """
    
    return summary.strip()


def render_report(data: Dict[str, Any], output_path: str) -> str:
    """
    Render a complete psychometric report as PDF
    
    Args:
        data: Dictionary containing:
            - student_name: str
            - grade: int
            - riasec_scores: Dict[str, int]
            - big5_scores: Dict[str, float]
            - cognitive_score: float
            - pe_fit: List[Tuple[str, float]]
            - test_date: str (optional)
        output_path: Path where PDF should be saved
    
    Returns:
        Path to the generated PDF file
    """
    
    # Ensure reports directory exists
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # Add timestamp if not provided
    if 'test_date' not in data:
        data['test_date'] = datetime.now().strftime("%B %d, %Y")
    
    # Generate AI-polished summary
    data['ai_summary'] = polish_summary_with_ai(data)
    
    # Prepare template data
    template_data = {
        **data,
        'report_generated': datetime.now().strftime("%B %d, %Y at %I:%M %p"),
        'riasec_traits': ['Realistic', 'Investigative', 'Artistic', 'Social', 'Enterprising', 'Conventional'],
        'big5_traits': ['Openness', 'Conscientiousness', 'Extraversion', 'Agreeableness', 'Neuroticism']
    }
    
    # Load and render template
    env = Environment(loader=FileSystemLoader('templates'))
    template = env.get_template('report_template.html')
    html_content = template.render(**template_data)
    
    # Generate PDF
    try:
        html_doc = HTML(string=html_content)
        html_doc.write_pdf(output_path)
        
        logger.info(f"Report generated successfully: {output_path}")
        return output_path
        
    except Exception as e:
        logger.error(f"Failed to generate PDF report: {e}")
        
        # Fallback: save as HTML
        html_path = output_path.replace('.pdf', '.html')
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"Saved as HTML instead: {html_path}")
        return html_path


def create_chart_data(scores: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare data for charts in the report
    
    Args:
        scores: Dictionary containing all psychometric scores
    
    Returns:
        Dictionary with chart-ready data
    """
    chart_data = {}
    
    # RIASEC radar chart data
    if 'riasec_scores' in scores:
        riasec = scores['riasec_scores']
        chart_data['riasec_chart'] = {
            'labels': list(riasec.keys()),
            'values': list(riasec.values()),
            'max_value': max(riasec.values()) if riasec.values() else 5
        }
    
    # Big Five bar chart data
    if 'big5_scores' in scores:
        big5 = scores['big5_scores']
        chart_data['big5_chart'] = {
            'labels': list(big5.keys()),
            'values': [round(v, 1) for v in big5.values()],
            'max_value': 5.0
        }
    
    # Career fit data
    if 'pe_fit' in scores:
        pe_fit = scores['pe_fit']
        chart_data['career_chart'] = {
            'labels': [career[0] for career in pe_fit],
            'values': [round(career[1] * 100, 1) for career in pe_fit],
            'max_value': 100
        }
    
    return chart_data


def generate_recommendations(data: Dict[str, Any]) -> Dict[str, List[str]]:
    """
    Generate personalized recommendations based on psychometric results
    
    Args:
        data: Complete psychometric data
    
    Returns:
        Dictionary with categorized recommendations
    """
    recommendations = {
        'academic': [],
        'career': [],
        'personal_development': [],
        'extracurricular': []
    }
    
    # RIASEC-based recommendations
    riasec = data.get('riasec_scores', {})
    if riasec:
        top_riasec = max(riasec, key=riasec.get)
        
        riasec_recommendations = {
            'Realistic': {
                'academic': ['Engineering courses', 'Applied sciences', 'Technical education'],
                'career': ['Engineering', 'Construction', 'Manufacturing'],
                'extracurricular': ['Robotics club', 'Maker space', 'Technical competitions']
            },
            'Investigative': {
                'academic': ['Advanced sciences', 'Research methods', 'Mathematics'],
                'career': ['Research scientist', 'Data analyst', 'Medical professional'],
                'extracurricular': ['Science fair', 'Research projects', 'Academic competitions']
            },
            'Artistic': {
                'academic': ['Creative writing', 'Visual arts', 'Music theory'],
                'career': ['Graphic design', 'Creative writing', 'Performing arts'],
                'extracurricular': ['Art club', 'Theater', 'Creative writing group']
            },
            'Social': {
                'academic': ['Psychology', 'Sociology', 'Communication'],
                'career': ['Teaching', 'Counseling', 'Social work'],
                'extracurricular': ['Volunteer work', 'Peer tutoring', 'Student government']
            },
            'Enterprising': {
                'academic': ['Business studies', 'Economics', 'Leadership courses'],
                'career': ['Business management', 'Sales', 'Entrepreneurship'],
                'extracurricular': ['Business club', 'Debate team', 'Student leadership']
            },
            'Conventional': {
                'academic': ['Accounting', 'Data management', 'Administrative studies'],
                'career': ['Accounting', 'Administration', 'Data analysis'],
                'extracurricular': ['Student council', 'Organization committees', 'Academic clubs']
            }
        }
        
        if top_riasec in riasec_recommendations:
            for category, items in riasec_recommendations[top_riasec].items():
                if category in recommendations:
                    recommendations[category].extend(items)
    
    # Big Five-based personal development recommendations
    big5 = data.get('big5_scores', {})
    if big5:
        if big5.get('Openness', 0) > 3.5:
            recommendations['personal_development'].append('Explore new creative outlets')
        if big5.get('Conscientiousness', 0) > 3.5:
            recommendations['personal_development'].append('Take on leadership responsibilities')
        if big5.get('Extraversion', 0) > 3.5:
            recommendations['personal_development'].append('Join social and collaborative activities')
        if big5.get('Agreeableness', 0) > 3.5:
            recommendations['personal_development'].append('Consider helping and mentoring roles')
    
    return recommendations
