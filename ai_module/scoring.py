# ai_module/scoring.py
import json
import logging
from typing import List, Dict, Tuple, Any
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from config.settings import settings

logger = logging.getLogger(__name__)

# Scoring mappings for different theories
RIASEC_SCORING = {
    "Realistic": {"A": 4, "B": 3, "C": 2, "D": 1},
    "Investigative": {"A": 4, "B": 3, "C": 2, "D": 1},
    "Artistic": {"A": 4, "B": 3, "C": 2, "D": 1},
    "Social": {"A": 4, "B": 3, "C": 2, "D": 1},
    "Enterprising": {"A": 4, "B": 3, "C": 2, "D": 1},
    "Conventional": {"A": 4, "B": 3, "C": 2, "D": 1}
}

BIG5_SCORING = {
    "Openness": {"A": 5, "B": 4, "C": 3, "D": 2, "E": 1},
    "Conscientiousness": {"A": 5, "B": 4, "C": 3, "D": 2, "E": 1},
    "Extraversion": {"A": 5, "B": 4, "C": 3, "D": 2, "E": 1},
    "Agreeableness": {"A": 5, "B": 4, "C": 3, "D": 2, "E": 1},
    "Neuroticism": {"A": 1, "B": 2, "C": 3, "D": 4, "E": 5}  # Reverse scored
}

def score_riasec(responses: List[Dict[str, Any]]) -> Dict[str, int]:
    """
    Score RIASEC responses and return trait scores
    
    Args:
        responses: List of response dictionaries with fields:
                  {
                      "question_id": str,
                      "trait": str,
                      "selected_option": int,  # 0-3 index
                      "theory": "RIASEC"
                  }
    
    Returns:
        Dictionary with RIASEC trait scores: {"Realistic": score, ...}
    """
    scores = {trait: 0 for trait in settings.RIASEC_TRAITS}
    trait_counts = {trait: 0 for trait in settings.RIASEC_TRAITS}
    
    for response in responses:
        if response.get("theory") != "RIASEC":
            continue
            
        trait = response.get("trait")
        selected_option = response.get("selected_option")
        
        if trait not in settings.RIASEC_TRAITS:
            logger.warning(f"Unknown RIASEC trait: {trait}")
            continue
        
        if selected_option is None or not (0 <= selected_option <= 3):
            logger.warning(f"Invalid selected_option: {selected_option}")
            continue
        
        # Convert option index to letter (0->A, 1->B, etc.)
        option_letter = chr(ord('A') + selected_option)
        
        # Get score for this trait and option
        if trait in RIASEC_SCORING and option_letter in RIASEC_SCORING[trait]:
            score = RIASEC_SCORING[trait][option_letter]
            scores[trait] += score
            trait_counts[trait] += 1
    
    # Average scores by number of questions per trait
    for trait in scores:
        if trait_counts[trait] > 0:
            scores[trait] = scores[trait] // trait_counts[trait]
    
    logger.info(f"RIASEC scores calculated: {scores}")
    return scores


def score_big5(responses: List[Dict[str, Any]]) -> Dict[str, float]:
    """
    Score Big Five responses and return trait scores
    
    Args:
        responses: List of response dictionaries
    
    Returns:
        Dictionary with Big Five trait scores (0.0-5.0): {"Openness": score, ...}
    """
    scores = {trait: 0.0 for trait in settings.BIG5_TRAITS}
    trait_counts = {trait: 0 for trait in settings.BIG5_TRAITS}
    
    for response in responses:
        if response.get("theory") != "Big5":
            continue
            
        trait = response.get("trait")
        selected_option = response.get("selected_option")
        
        if trait not in settings.BIG5_TRAITS:
            logger.warning(f"Unknown Big5 trait: {trait}")
            continue
        
        if selected_option is None or not (0 <= selected_option <= 3):
            logger.warning(f"Invalid selected_option: {selected_option}")
            continue
        
        # Convert option index to letter
        option_letter = chr(ord('A') + selected_option)
        
        # Get score for this trait and option
        if trait in BIG5_SCORING and option_letter in BIG5_SCORING[trait]:
            score = BIG5_SCORING[trait][option_letter]
            scores[trait] += score
            trait_counts[trait] += 1
    
    # Average scores by number of questions per trait
    for trait in scores:
        if trait_counts[trait] > 0:
            scores[trait] = scores[trait] / trait_counts[trait]
    
    logger.info(f"Big5 scores calculated: {scores}")
    return scores


def score_cognitive(responses: List[Dict[str, Any]]) -> float:
    """
    Score cognitive responses and return overall cognitive ability score
    
    Args:
        responses: List of response dictionaries
    
    Returns:
        Overall cognitive score (0.0-100.0)
    """
    correct_answers = 0
    total_questions = 0
    
    for response in responses:
        if response.get("theory") != "Cognitive":
            continue
            
        selected_option = response.get("selected_option")
        correct_index = response.get("correct_index")
        
        if selected_option is None or correct_index is None:
            logger.warning("Missing selected_option or correct_index in cognitive response")
            continue
        
        total_questions += 1
        if selected_option == correct_index:
            correct_answers += 1
    
    if total_questions == 0:
        logger.warning("No cognitive questions found")
        return 0.0
    
    cognitive_score = (correct_answers / total_questions) * 100.0
    logger.info(f"Cognitive score calculated: {cognitive_score:.2f}% ({correct_answers}/{total_questions})")
    return cognitive_score


def load_onet_data() -> Dict[str, List[float]]:
    """
    Load O*NET RIASEC vectors for career clusters
    
    Returns:
        Dictionary mapping career cluster names to RIASEC vectors
    """
    try:
        with open("data/onet_riasec.json", "r") as f:
            onet_data = json.load(f)
        return onet_data
    except FileNotFoundError:
        logger.warning("O*NET data file not found, using sample data")
        # Sample O*NET RIASEC vectors for demonstration
        return {
            "Engineering": [4.2, 4.8, 2.1, 2.3, 3.1, 3.5],
            "Healthcare": [2.8, 4.1, 2.9, 4.7, 3.2, 3.8],
            "Education": [2.1, 3.4, 3.8, 4.9, 3.6, 3.2],
            "Business": [2.9, 3.2, 3.1, 4.1, 4.8, 4.2],
            "Arts": [2.3, 3.1, 4.9, 3.7, 3.4, 2.1],
            "Technology": [3.1, 4.6, 3.2, 2.8, 3.9, 3.7],
            "Science": [3.4, 4.9, 3.3, 3.1, 2.9, 3.6],
            "Social Services": [2.2, 3.3, 3.6, 4.8, 3.1, 3.4],
            "Finance": [2.7, 3.8, 2.4, 3.2, 4.3, 4.6],
            "Manufacturing": [4.1, 3.2, 2.3, 2.6, 3.4, 4.1]
        }


def compute_pe_fit(riasec_scores: Dict[str, int]) -> List[Tuple[str, float]]:
    """
    Compute Person-Environment fit using cosine similarity with O*NET data
    
    Args:
        riasec_scores: Dictionary with RIASEC trait scores
    
    Returns:
        List of top 3 career clusters with similarity scores: [(cluster, similarity), ...]
    """
    # Convert RIASEC scores to vector
    riasec_vector = np.array([
        riasec_scores.get("Realistic", 0),
        riasec_scores.get("Investigative", 0),
        riasec_scores.get("Artistic", 0),
        riasec_scores.get("Social", 0),
        riasec_scores.get("Enterprising", 0),
        riasec_scores.get("Conventional", 0)
    ]).reshape(1, -1)
    
    # Load O*NET career cluster data
    onet_data = load_onet_data()
    
    similarities = []
    
    for cluster_name, cluster_vector in onet_data.items():
        cluster_array = np.array(cluster_vector).reshape(1, -1)
        
        # Compute cosine similarity
        similarity = cosine_similarity(riasec_vector, cluster_array)[0][0]
        similarities.append((cluster_name, float(similarity)))
    
    # Sort by similarity (descending) and return top 3
    similarities.sort(key=lambda x: x[1], reverse=True)
    top_3 = similarities[:3]
    
    logger.info(f"Top 3 PE fit matches: {top_3}")
    return top_3


def calculate_composite_scores(all_responses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Calculate all psychometric scores from a complete set of responses
    
    Args:
        all_responses: List of all student responses across all theories
    
    Returns:
        Dictionary containing all calculated scores
    """
    results = {}
    
    # Calculate RIASEC scores
    riasec_responses = [r for r in all_responses if r.get("theory") == "RIASEC"]
    if riasec_responses:
        results["riasec_scores"] = score_riasec(riasec_responses)
        results["pe_fit"] = compute_pe_fit(results["riasec_scores"])
    
    # Calculate Big5 scores
    big5_responses = [r for r in all_responses if r.get("theory") == "Big5"]
    if big5_responses:
        results["big5_scores"] = score_big5(big5_responses)
    
    # Calculate Cognitive score
    cognitive_responses = [r for r in all_responses if r.get("theory") == "Cognitive"]
    if cognitive_responses:
        results["cognitive_score"] = score_cognitive(cognitive_responses)
    
    # Calculate other theory scores (Hogan, CPI) - simplified scoring
    for theory in ["Hogan", "CPI"]:
        theory_responses = [r for r in all_responses if r.get("theory") == theory]
        if theory_responses:
            results[f"{theory.lower()}_scores"] = score_generic_theory(theory_responses, theory)
    
    return results


def score_generic_theory(responses: List[Dict[str, Any]], theory: str) -> Dict[str, float]:
    """
    Generic scoring function for Hogan and CPI theories
    
    Args:
        responses: List of responses for the theory
        theory: Theory name ("Hogan" or "CPI")
    
    Returns:
        Dictionary with trait scores
    """
    if theory == "Hogan":
        traits = settings.HOGAN_TRAITS
    elif theory == "CPI":
        traits = settings.CPI_TRAITS
    else:
        return {}
    
    scores = {trait: 0.0 for trait in traits}
    trait_counts = {trait: 0 for trait in traits}
    
    for response in responses:
        trait = response.get("trait")
        selected_option = response.get("selected_option")
        
        if trait not in traits or selected_option is None:
            continue
        
        # Simple scoring: option index + 1 (1-4 scale)
        score = selected_option + 1
        scores[trait] += score
        trait_counts[trait] += 1
    
    # Average scores
    for trait in scores:
        if trait_counts[trait] > 0:
            scores[trait] = scores[trait] / trait_counts[trait]
    
    return scores
