# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyA8a-W_vIoWl3yBSEqwploQd3IPXbg2qOg

# Database Configuration (if needed)
DATABASE_URL=sqlite:///./psychometric.db

# External API Endpoints
SCHOLARSHIPS_API_URL=https://api.example.com/scholarships
LOANS_API_URL=https://api.example.com/loans

# Report Generation Settings
REPORTS_DIR=./static/reports
BASE_URL=http://localhost:8000

# Development Settings
DEBUG=True
LOG_LEVEL=INFO
