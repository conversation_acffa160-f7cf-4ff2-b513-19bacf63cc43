# main.py
import os
import uuid
import logging
from datetime import datetime
from typing import List, Dict, Any, Optional
from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, Query
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn

from config.settings import settings
from ai_module import (
    generate_question_batch,
    score_riasec,
    score_big5, 
    score_cognitive,
    compute_pe_fit,
    render_report,
    fetch_scholarships,
    fetch_loans
)
from ai_module.scoring import calculate_composite_scores

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Psychometric Testing Platform API",
    description="AI-powered psychometric assessment platform for students",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Ensure directories exist
os.makedirs(settings.REPORTS_DIR, exist_ok=True)
os.makedirs("static", exist_ok=True)

# In-memory storage for demo (replace with database in production)
test_sessions = {}
student_responses = {}

# Pydantic models
class QuestionGenerationRequest(BaseModel):
    grade: int = Field(..., ge=1, le=12, description="Student grade level")
    theory: str = Field(..., description="Psychometric theory")
    trait: str = Field(..., description="Specific trait within theory")
    difficulty: str = Field(..., description="Question difficulty level")
    num_questions: int = Field(default=20, ge=1, le=100, description="Number of questions")

class StudentResponse(BaseModel):
    question_id: str
    theory: str
    trait: str
    selected_option: int = Field(..., ge=0, le=3)
    correct_index: Optional[int] = Field(None, ge=0, le=3)
    time_taken: Optional[float] = None

class TestSubmission(BaseModel):
    session_id: str
    student_name: str
    grade: int = Field(..., ge=1, le=12)
    responses: List[StudentResponse]

class TestSession(BaseModel):
    student_name: str
    grade: int = Field(..., ge=1, le=12)
    theories: List[str] = Field(default=["RIASEC", "Big5", "Cognitive"])

# API Routes

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Psychometric Testing Platform API",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.now().isoformat()
    }

@app.post("/generate_questions")
async def generate_questions_endpoint(request: QuestionGenerationRequest):
    """
    Generate psychometric questions using AI
    
    Args:
        request: Question generation parameters
    
    Returns:
        List of generated questions
    """
    try:
        logger.info(f"Generating questions: {request.dict()}")
        
        questions = generate_question_batch(
            grade=request.grade,
            theory=request.theory,
            trait=request.trait,
            difficulty=request.difficulty,
            num_questions=request.num_questions
        )
        
        return {
            "success": True,
            "questions": questions,
            "count": len(questions),
            "metadata": {
                "grade": request.grade,
                "theory": request.theory,
                "trait": request.trait,
                "difficulty": request.difficulty
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to generate questions: {e}")
        raise HTTPException(status_code=500, detail=f"Question generation failed: {str(e)}")

@app.post("/create_session")
async def create_test_session(session_request: TestSession):
    """
    Create a new test session for a student
    
    Args:
        session_request: Student and test configuration
    
    Returns:
        Session ID and configuration
    """
    try:
        session_id = str(uuid.uuid4())
        
        # Store session data
        test_sessions[session_id] = {
            "student_name": session_request.student_name,
            "grade": session_request.grade,
            "theories": session_request.theories,
            "created_at": datetime.now().isoformat(),
            "status": "active"
        }
        
        logger.info(f"Created test session {session_id} for {session_request.student_name}")
        
        return {
            "success": True,
            "session_id": session_id,
            "student_name": session_request.student_name,
            "grade": session_request.grade,
            "theories": session_request.theories
        }
        
    except Exception as e:
        logger.error(f"Failed to create session: {e}")
        raise HTTPException(status_code=500, detail=f"Session creation failed: {str(e)}")

@app.post("/submit_test")
async def submit_test_responses(submission: TestSubmission, background_tasks: BackgroundTasks):
    """
    Submit student test responses and calculate scores
    
    Args:
        submission: Complete test submission with all responses
        background_tasks: FastAPI background tasks
    
    Returns:
        Calculated scores and session information
    """
    try:
        session_id = submission.session_id
        
        # Validate session exists
        if session_id not in test_sessions:
            raise HTTPException(status_code=404, detail="Test session not found")
        
        session_data = test_sessions[session_id]
        
        # Convert responses to the format expected by scoring functions
        response_dicts = [response.dict() for response in submission.responses]
        
        # Calculate all scores
        scores = calculate_composite_scores(response_dicts)
        
        # Store results
        student_responses[session_id] = {
            "student_name": submission.student_name,
            "grade": submission.grade,
            "responses": response_dicts,
            "scores": scores,
            "submitted_at": datetime.now().isoformat()
        }
        
        # Update session status
        test_sessions[session_id]["status"] = "completed"
        test_sessions[session_id]["completed_at"] = datetime.now().isoformat()
        
        logger.info(f"Test submitted for session {session_id}")
        
        return {
            "success": True,
            "session_id": session_id,
            "student_name": submission.student_name,
            "scores": scores,
            "message": "Test submitted successfully. Report generation initiated."
        }
        
    except Exception as e:
        logger.error(f"Failed to submit test: {e}")
        raise HTTPException(status_code=500, detail=f"Test submission failed: {str(e)}")

@app.get("/generate_report")
async def generate_student_report(session_id: str = Query(..., description="Test session ID")):
    """
    Generate and return a PDF report for a completed test
    
    Args:
        session_id: Test session identifier
    
    Returns:
        PDF file download
    """
    try:
        # Validate session and results exist
        if session_id not in student_responses:
            raise HTTPException(status_code=404, detail="Test results not found")
        
        results = student_responses[session_id]
        
        # Prepare report data
        report_data = {
            "student_name": results["student_name"],
            "grade": results["grade"],
            "test_date": datetime.fromisoformat(results["submitted_at"]).strftime("%B %d, %Y"),
            **results["scores"]
        }
        
        # Generate report filename
        safe_name = "".join(c for c in results["student_name"] if c.isalnum() or c in (' ', '-', '_')).rstrip()
        filename = f"psychometric_report_{safe_name}_{session_id[:8]}.pdf"
        output_path = os.path.join(settings.REPORTS_DIR, filename)
        
        # Generate the report
        generated_path = render_report(report_data, output_path)
        
        logger.info(f"Report generated: {generated_path}")
        
        # Return the PDF file
        return FileResponse(
            path=generated_path,
            filename=filename,
            media_type="application/pdf"
        )
        
    except Exception as e:
        logger.error(f"Failed to generate report: {e}")
        raise HTTPException(status_code=500, detail=f"Report generation failed: {str(e)}")

@app.get("/scholarships")
async def get_scholarships(
    grade: Optional[int] = Query(None, ge=1, le=12, description="Filter by grade level"),
    limit: int = Query(50, ge=1, le=100, description="Maximum number of results")
):
    """
    Fetch available scholarships, optionally filtered by grade
    
    Args:
        grade: Student grade level for filtering
        limit: Maximum number of scholarships to return
    
    Returns:
        List of available scholarships
    """
    try:
        scholarships = await fetch_scholarships(grade=grade, limit=limit)
        
        return {
            "success": True,
            "scholarships": scholarships,
            "count": len(scholarships),
            "filters": {
                "grade": grade,
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch scholarships: {e}")
        raise HTTPException(status_code=500, detail=f"Scholarship fetch failed: {str(e)}")

@app.get("/loans")
async def get_loans(
    grade: Optional[int] = Query(None, ge=1, le=12, description="Filter by grade level"),
    limit: int = Query(20, ge=1, le=50, description="Maximum number of results")
):
    """
    Fetch available loans and financial aid, optionally filtered by grade
    
    Args:
        grade: Student grade level for filtering
        limit: Maximum number of loans to return
    
    Returns:
        List of available loans and financial aid
    """
    try:
        loans = await fetch_loans(grade=grade, limit=limit)
        
        return {
            "success": True,
            "loans": loans,
            "count": len(loans),
            "filters": {
                "grade": grade,
                "limit": limit
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to fetch loans: {e}")
        raise HTTPException(status_code=500, detail=f"Loan fetch failed: {str(e)}")

@app.get("/session/{session_id}")
async def get_session_info(session_id: str):
    """
    Get information about a test session
    
    Args:
        session_id: Test session identifier
    
    Returns:
        Session information and status
    """
    if session_id not in test_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session_data = test_sessions[session_id]
    
    # Add results if available
    if session_id in student_responses:
        session_data["has_results"] = True
        session_data["scores_summary"] = student_responses[session_id]["scores"]
    else:
        session_data["has_results"] = False
    
    return {
        "success": True,
        "session": session_data
    }

@app.get("/health")
async def health_check():
    """Detailed health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "environment": {
            "debug": settings.DEBUG,
            "gemini_configured": bool(settings.GEMINI_API_KEY),
            "reports_dir": settings.REPORTS_DIR
        },
        "statistics": {
            "active_sessions": len([s for s in test_sessions.values() if s["status"] == "active"]),
            "completed_tests": len(student_responses),
            "total_sessions": len(test_sessions)
        }
    }

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"success": False, "error": "Resource not found", "detail": str(exc.detail)}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"success": False, "error": "Internal server error", "detail": "An unexpected error occurred"}
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
