# tests/test_scoring.py
import pytest
from ai_module.scoring import score_riasec, score_big5, score_cognitive, compute_pe_fit

class TestScoring:
    
    def test_score_riasec(self):
        """Test RIASEC scoring function"""
        sample_responses = [
            {
                "question_id": "q1",
                "theory": "RIASEC",
                "trait": "Realistic",
                "selected_option": 0  # Option A
            },
            {
                "question_id": "q2",
                "theory": "RIASEC",
                "trait": "Realistic",
                "selected_option": 1  # Option B
            },
            {
                "question_id": "q3",
                "theory": "RIASEC",
                "trait": "Investigative",
                "selected_option": 0  # Option A
            }
        ]
        
        scores = score_riasec(sample_responses)
        
        # Check that all RIASEC traits are present
        expected_traits = ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
        for trait in expected_traits:
            assert trait in scores
        
        # Check that Realistic has a score (2 responses)
        assert scores["Realistic"] > 0
        
        # Check that Investigative has a score (1 response)
        assert scores["Investigative"] > 0
    
    def test_score_big5(self):
        """Test Big Five scoring function"""
        sample_responses = [
            {
                "question_id": "q1",
                "theory": "Big5",
                "trait": "Openness",
                "selected_option": 0  # Option A
            },
            {
                "question_id": "q2",
                "theory": "Big5",
                "trait": "Conscientiousness",
                "selected_option": 2  # Option C
            }
        ]
        
        scores = score_big5(sample_responses)
        
        # Check that all Big5 traits are present
        expected_traits = ["Openness", "Conscientiousness", "Extraversion", "Agreeableness", "Neuroticism"]
        for trait in expected_traits:
            assert trait in scores
        
        # Check score ranges (0.0 to 5.0)
        for trait, score in scores.items():
            assert 0.0 <= score <= 5.0
    
    def test_score_cognitive(self):
        """Test cognitive scoring function"""
        sample_responses = [
            {
                "question_id": "q1",
                "theory": "Cognitive",
                "trait": "Verbal",
                "selected_option": 2,
                "correct_index": 2  # Correct answer
            },
            {
                "question_id": "q2",
                "theory": "Cognitive",
                "trait": "Numerical",
                "selected_option": 1,
                "correct_index": 3  # Incorrect answer
            },
            {
                "question_id": "q3",
                "theory": "Cognitive",
                "trait": "Spatial",
                "selected_option": 0,
                "correct_index": 0  # Correct answer
            }
        ]
        
        score = score_cognitive(sample_responses)
        
        # Should be 66.67% (2 out of 3 correct)
        expected_score = (2 / 3) * 100
        assert abs(score - expected_score) < 0.1
        
        # Score should be between 0 and 100
        assert 0.0 <= score <= 100.0
    
    def test_compute_pe_fit(self):
        """Test Person-Environment fit computation"""
        sample_riasec_scores = {
            "Realistic": 4,
            "Investigative": 5,
            "Artistic": 2,
            "Social": 3,
            "Enterprising": 2,
            "Conventional": 3
        }
        
        pe_fit = compute_pe_fit(sample_riasec_scores)
        
        # Should return top 3 matches
        assert len(pe_fit) == 3
        
        # Each match should be a tuple of (career_name, similarity_score)
        for career, similarity in pe_fit:
            assert isinstance(career, str)
            assert isinstance(similarity, float)
            assert -1.0 <= similarity <= 1.0  # Cosine similarity range
        
        # Results should be sorted by similarity (descending)
        similarities = [match[1] for match in pe_fit]
        assert similarities == sorted(similarities, reverse=True)
    
    def test_score_riasec_empty_responses(self):
        """Test RIASEC scoring with empty responses"""
        scores = score_riasec([])
        
        # Should return zero scores for all traits
        expected_traits = ["Realistic", "Investigative", "Artistic", "Social", "Enterprising", "Conventional"]
        for trait in expected_traits:
            assert scores[trait] == 0
    
    def test_score_riasec_invalid_responses(self):
        """Test RIASEC scoring with invalid responses"""
        invalid_responses = [
            {
                "question_id": "q1",
                "theory": "RIASEC",
                "trait": "InvalidTrait",  # Invalid trait
                "selected_option": 0
            },
            {
                "question_id": "q2",
                "theory": "RIASEC",
                "trait": "Realistic",
                "selected_option": 5  # Invalid option (should be 0-3)
            }
        ]
        
        scores = score_riasec(invalid_responses)
        
        # Should handle invalid data gracefully
        assert all(score == 0 for score in scores.values())

if __name__ == "__main__":
    pytest.main([__file__])
