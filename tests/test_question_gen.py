# tests/test_question_gen.py
import pytest
import json
from unittest.mock import Mock, patch
from ai_module.question_gen import generate_question_batch, validate_question_schema

class TestQuestionGeneration:
    
    @patch('ai_module.question_gen.model')
    def test_generate_question_batch_success(self, mock_model):
        """Test successful question generation"""
        # Mock Gemini response
        mock_response = Mock()
        mock_response.text = '''[
            {
                "grade": 9,
                "theory": "RIASEC",
                "trait": "Realistic",
                "question": "Which activity would you most enjoy?",
                "options": ["Building things", "Reading books", "Painting", "Teaching others"],
                "correct_index": 0,
                "difficulty": "easy"
            }
        ]'''
        mock_model.generate_content.return_value = mock_response
        
        # Test the function
        result = generate_question_batch(
            grade=9,
            theory="RIASEC",
            trait="Realistic",
            difficulty="easy",
            num_questions=1
        )
        
        # Assertions
        assert len(result) == 1
        assert result[0]["grade"] == 9
        assert result[0]["theory"] == "RIASEC"
        assert result[0]["trait"] == "Realistic"
        assert len(result[0]["options"]) == 4
        assert 0 <= result[0]["correct_index"] <= 3
    
    @patch('ai_module.question_gen.model')
    def test_generate_question_batch_invalid_json(self, mock_model):
        """Test handling of invalid JSON response"""
        mock_response = Mock()
        mock_response.text = "Invalid JSON response"
        mock_model.generate_content.return_value = mock_response
        
        with pytest.raises(ValueError, match="Invalid JSON response"):
            generate_question_batch(
                grade=9,
                theory="RIASEC",
                trait="Realistic",
                difficulty="easy",
                num_questions=1
            )
    
    def test_validate_question_schema_valid(self):
        """Test question schema validation with valid data"""
        valid_question = {
            "grade": 9,
            "theory": "RIASEC",
            "trait": "Realistic",
            "question": "Test question?",
            "options": ["A", "B", "C", "D"],
            "correct_index": 0,
            "difficulty": "easy"
        }
        
        assert validate_question_schema(valid_question) == True
    
    def test_validate_question_schema_invalid(self):
        """Test question schema validation with invalid data"""
        invalid_question = {
            "grade": 9,
            "theory": "RIASEC",
            "trait": "Realistic",
            "question": "Test question?",
            "options": ["A", "B"],  # Only 2 options instead of 4
            "correct_index": 0,
            "difficulty": "easy"
        }
        
        assert validate_question_schema(invalid_question) == False
    
    def test_validate_question_schema_missing_fields(self):
        """Test question schema validation with missing fields"""
        incomplete_question = {
            "grade": 9,
            "theory": "RIASEC",
            "question": "Test question?",
            "options": ["A", "B", "C", "D"]
            # Missing trait, correct_index, difficulty
        }
        
        assert validate_question_schema(incomplete_question) == False

if __name__ == "__main__":
    pytest.main([__file__])
