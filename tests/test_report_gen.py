# tests/test_report_gen.py
import pytest
import os
import tempfile
from unittest.mock import Mock, patch
from ai_module.report_gen import render_report, polish_summary_with_ai, generate_fallback_summary

class TestReportGeneration:
    
    def test_generate_fallback_summary(self):
        """Test fallback summary generation"""
        sample_data = {
            "student_name": "<PERSON>",
            "grade": 10,
            "riasec_scores": {"Realistic": 4, "Investigative": 5},
            "big5_scores": {"Openness": 4.2, "Conscientiousness": 3.8},
            "cognitive_score": 85.5
        }
        
        summary = generate_fallback_summary(sample_data)
        
        assert isinstance(summary, str)
        assert len(summary) > 50  # Should be a substantial summary
        assert "<PERSON>" in summary
        assert "psychometric assessment" in summary.lower()
    
    @patch('ai_module.report_gen.model')
    def test_polish_summary_with_ai_success(self, mock_model):
        """Test AI summary polishing with successful response"""
        mock_response = Mock()
        mock_response.text = "This is a polished AI-generated summary for the student."
        mock_model.generate_content.return_value = mock_response
        
        sample_data = {
            "student_name": "<PERSON>",
            "grade": 11,
            "riasec_scores": {"Realistic": 3, "Investigative": 4},
            "big5_scores": {"Openness": 4.0, "Conscientiousness": 3.5},
            "cognitive_score": 78.0
        }
        
        summary = polish_summary_with_ai(sample_data)
        
        assert summary == "This is a polished AI-generated summary for the student."
        mock_model.generate_content.assert_called_once()
    
    @patch('ai_module.report_gen.model')
    def test_polish_summary_with_ai_failure(self, mock_model):
        """Test AI summary polishing with API failure"""
        mock_model.generate_content.side_effect = Exception("API Error")
        
        sample_data = {
            "student_name": "Bob Johnson",
            "grade": 9,
            "riasec_scores": {"Realistic": 2, "Investigative": 3},
            "big5_scores": {"Openness": 3.0, "Conscientiousness": 4.0},
            "cognitive_score": 72.0
        }
        
        summary = polish_summary_with_ai(sample_data)
        
        # Should fall back to basic summary
        assert isinstance(summary, str)
        assert "Bob Johnson" in summary
        assert len(summary) > 50
    
    @patch('ai_module.report_gen.HTML')
    @patch('ai_module.report_gen.Environment')
    def test_render_report_success(self, mock_env, mock_html):
        """Test successful report rendering"""
        # Mock template rendering
        mock_template = Mock()
        mock_template.render.return_value = "<html>Test Report</html>"
        mock_loader = Mock()
        mock_loader.get_template.return_value = mock_template
        mock_env.return_value = mock_loader
        
        # Mock HTML to PDF conversion
        mock_html_doc = Mock()
        mock_html.return_value = mock_html_doc
        
        sample_data = {
            "student_name": "Alice Brown",
            "grade": 12,
            "riasec_scores": {"Realistic": 3, "Investigative": 4, "Artistic": 2, "Social": 5, "Enterprising": 3, "Conventional": 2},
            "big5_scores": {"Openness": 4.1, "Conscientiousness": 3.7, "Extraversion": 3.9, "Agreeableness": 4.2, "Neuroticism": 2.8},
            "cognitive_score": 88.5,
            "pe_fit": [("Healthcare", 0.85), ("Education", 0.78), ("Social Services", 0.72)]
        }
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            result_path = render_report(sample_data, output_path)
            
            # Verify the function completed
            assert result_path == output_path
            mock_html_doc.write_pdf.assert_called_once_with(output_path)
            
        finally:
            # Clean up
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    @patch('ai_module.report_gen.HTML')
    @patch('ai_module.report_gen.Environment')
    def test_render_report_pdf_failure_html_fallback(self, mock_env, mock_html):
        """Test report rendering with PDF failure, falling back to HTML"""
        # Mock template rendering
        mock_template = Mock()
        mock_template.render.return_value = "<html>Test Report</html>"
        mock_loader = Mock()
        mock_loader.get_template.return_value = mock_template
        mock_env.return_value = mock_loader
        
        # Mock HTML to PDF conversion failure
        mock_html_doc = Mock()
        mock_html_doc.write_pdf.side_effect = Exception("PDF generation failed")
        mock_html.return_value = mock_html_doc
        
        sample_data = {
            "student_name": "Charlie Davis",
            "grade": 10,
            "riasec_scores": {"Realistic": 4, "Investigative": 3, "Artistic": 3, "Social": 2, "Enterprising": 4, "Conventional": 5},
            "big5_scores": {"Openness": 3.2, "Conscientiousness": 4.1, "Extraversion": 2.8, "Agreeableness": 3.9, "Neuroticism": 3.1},
            "cognitive_score": 76.0
        }
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            result_path = render_report(sample_data, output_path)
            
            # Should return HTML path instead of PDF
            expected_html_path = output_path.replace('.pdf', '.html')
            assert result_path == expected_html_path
            
            # Verify HTML file was created
            assert os.path.exists(expected_html_path)
            
            # Clean up HTML file
            if os.path.exists(expected_html_path):
                os.unlink(expected_html_path)
                
        finally:
            # Clean up PDF file if it exists
            if os.path.exists(output_path):
                os.unlink(output_path)
    
    def test_render_report_missing_data(self):
        """Test report rendering with minimal data"""
        minimal_data = {
            "student_name": "Test Student",
            "grade": 9
        }
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            output_path = tmp_file.name
        
        try:
            # Should not raise an exception even with minimal data
            result_path = render_report(minimal_data, output_path)
            assert isinstance(result_path, str)
            
        finally:
            # Clean up
            for ext in ['.pdf', '.html']:
                test_path = output_path.replace('.pdf', ext)
                if os.path.exists(test_path):
                    os.unlink(test_path)

if __name__ == "__main__":
    pytest.main([__file__])
