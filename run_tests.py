# run_tests.py
"""
Test runner script for the psychometric testing platform
"""
import sys
import subprocess
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*50}")
    
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    
    if result.stdout:
        print("STDOUT:")
        print(result.stdout)
    
    if result.stderr:
        print("STDERR:")
        print(result.stderr)
    
    if result.returncode != 0:
        print(f"❌ {description} failed with return code {result.returncode}")
        return False
    else:
        print(f"✅ {description} completed successfully")
        return True

def main():
    """Main test runner"""
    print("🧪 Psychometric Testing Platform - Test Suite")
    print("=" * 60)
    
    # Change to project directory
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    # Check if virtual environment is activated
    if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("⚠️  Warning: Virtual environment not detected. Consider activating your venv.")
    
    # Install test dependencies
    print("\n📦 Installing test dependencies...")
    install_cmd = "pip install pytest pytest-asyncio pytest-cov"
    if not run_command(install_cmd, "Installing test dependencies"):
        return 1
    
    # Run linting (if flake8 is available)
    print("\n🔍 Running code linting...")
    lint_cmd = "python -m flake8 ai_module/ main.py config/ --max-line-length=120 --ignore=E203,W503"
    run_command(lint_cmd, "Code linting (optional)")
    
    # Run type checking (if mypy is available)
    print("\n🔍 Running type checking...")
    type_cmd = "python -m mypy ai_module/ main.py --ignore-missing-imports"
    run_command(type_cmd, "Type checking (optional)")
    
    # Run unit tests
    print("\n🧪 Running unit tests...")
    test_cmd = "python -m pytest tests/ -v --tb=short"
    if not run_command(test_cmd, "Unit tests"):
        return 1
    
    # Run tests with coverage
    print("\n📊 Running tests with coverage...")
    coverage_cmd = "python -m pytest tests/ --cov=ai_module --cov-report=html --cov-report=term"
    run_command(coverage_cmd, "Test coverage analysis")
    
    # Test API endpoints (basic smoke test)
    print("\n🌐 Running API smoke tests...")
    api_test_cmd = "python -c \"import requests; print('API test would go here - start server first')\""
    run_command(api_test_cmd, "API smoke tests (placeholder)")
    
    print("\n" + "="*60)
    print("🎉 Test suite completed!")
    print("📊 Check htmlcov/index.html for detailed coverage report")
    print("="*60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
